import React, { useEffect, useRef, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft,
  ArrowRight,
  Calculator,
  User,
  Mail,
  Phone,
  FileText,
  Zap,
  Droplets,
  Settings,
  Shield,
  AlertCircle,
  CheckCircle2
} from 'lucide-react';
import gsap from 'gsap';
import { useNavigate } from 'react-router-dom';

// Types for form data
interface FormData {
  // Step 1: Personal Info
  name: string;
  email: string;
  phone: string;
  company?: string;

  // Step 2: Project Details
  service: string;
  projectType: string;
  timeline: string;
  budget: string;

  // Step 3: Requirements
  description: string;
  requirements: string[];
  files: File[];

  // Step 4: Additional Info
  urgency: string;
  preferredContact: string;
  additionalNotes?: string;
}

interface FormErrors {
  [key: string]: string;
}

interface ServiceOption {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  basePrice: number;
  features: string[];
}

const QuoteRequest: React.FC = () => {
  const formRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  // Form state
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    service: '',
    projectType: '',
    timeline: '',
    budget: '',
    description: '',
    requirements: [],
    files: [],
    urgency: '',
    preferredContact: '',
    additionalNotes: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [estimatedCost, setEstimatedCost] = useState(0);

  // Service options
  const serviceOptions: ServiceOption[] = [
    {
      id: 'water-treatment',
      name: 'Water Treatment Solutions',
      description: 'Advanced filtration and purification systems',
      icon: <Droplets className="w-8 h-8" />,
      basePrice: 5000,
      features: ['Custom filtration', 'Quality monitoring', '24/7 support']
    },
    {
      id: 'consulting',
      name: 'Consulting Services',
      description: 'Expert guidance and strategic planning',
      icon: <Settings className="w-8 h-8" />,
      basePrice: 2500,
      features: ['Strategic planning', 'Process optimization', 'Compliance guidance']
    },
    {
      id: 'maintenance',
      name: 'Maintenance & Support',
      description: 'Ongoing system maintenance and support',
      icon: <Shield className="w-8 h-8" />,
      basePrice: 1500,
      features: ['Regular maintenance', 'Emergency support', 'Performance monitoring']
    },
    {
      id: 'custom',
      name: 'Custom Solutions',
      description: 'Tailored solutions for unique requirements',
      icon: <Zap className="w-8 h-8" />,
      basePrice: 7500,
      features: ['Custom development', 'Integration services', 'Specialized equipment']
    }
  ];

  const totalSteps = 4;

  // Form validation
  const validateStep = useCallback((step: number): boolean => {
    const newErrors: FormErrors = {};

    switch (step) {
      case 1:
        if (!formData.name.trim()) newErrors.name = 'Name is required';
        if (!formData.email.trim()) {
          newErrors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
          newErrors.email = 'Please enter a valid email';
        }
        if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';
        break;

      case 2:
        if (!formData.service) newErrors.service = 'Please select a service';
        if (!formData.projectType) newErrors.projectType = 'Please specify project type';
        if (!formData.timeline) newErrors.timeline = 'Please select a timeline';
        if (!formData.budget) newErrors.budget = 'Please select a budget range';
        break;

      case 3:
        if (!formData.description.trim()) newErrors.description = 'Project description is required';
        if (formData.requirements.length === 0) newErrors.requirements = 'Please select at least one requirement';
        break;

      case 4:
        if (!formData.urgency) newErrors.urgency = 'Please specify urgency level';
        if (!formData.preferredContact) newErrors.preferredContact = 'Please select preferred contact method';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Calculate estimated cost
  const calculateEstimatedCost = useCallback(() => {
    const selectedService = serviceOptions.find(s => s.id === formData.service);
    if (!selectedService) return 0;

    let cost = selectedService.basePrice;

    // Timeline multiplier
    const timelineMultipliers: { [key: string]: number } = {
      'urgent': 1.5,
      '1-month': 1.2,
      '3-months': 1.0,
      '6-months': 0.9,
      'flexible': 0.8
    };

    // Budget adjustments
    const budgetMultipliers: { [key: string]: number } = {
      'under-10k': 0.8,
      '10k-25k': 1.0,
      '25k-50k': 1.2,
      '50k-100k': 1.5,
      'over-100k': 2.0
    };

    cost *= timelineMultipliers[formData.timeline] || 1;
    cost *= budgetMultipliers[formData.budget] || 1;

    // Requirements multiplier
    cost *= (1 + formData.requirements.length * 0.1);

    return Math.round(cost);
  }, [formData, serviceOptions]);

  // Update estimated cost when relevant fields change
  useEffect(() => {
    setEstimatedCost(calculateEstimatedCost());
  }, [formData.service, formData.timeline, formData.budget, formData.requirements, calculateEstimatedCost]);

  // Handle form field changes
  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Handle step navigation
  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateStep(currentStep)) return;

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Show success message or redirect
      console.log('Form submitted:', formData);

      // Reset form or redirect to success page
      // navigate('/quote-success');

    } catch (error) {
      console.error('Submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Animate form entrance
  useEffect(() => {
    if (formRef.current) {
      gsap.fromTo(
        formRef.current.children,
        { y: 20, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 0.6,
          stagger: 0.1,
          ease: 'power3.out',
        }
      );
    }
  }, [currentStep]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-cyan-50 to-teal-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden">
      {/* Animated background elements - optimized for mobile */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-20 -right-20 sm:-top-40 sm:-right-40 w-40 h-40 sm:w-80 sm:h-80 bg-gradient-to-br from-blue-400/20 to-cyan-400/20 rounded-full blur-2xl sm:blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-20 -left-20 sm:-bottom-40 sm:-left-40 w-40 h-40 sm:w-80 sm:h-80 bg-gradient-to-br from-teal-400/20 to-blue-400/20 rounded-full blur-2xl sm:blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 sm:w-96 sm:h-96 bg-gradient-to-br from-cyan-400/10 to-teal-400/10 rounded-full blur-2xl sm:blur-3xl animate-spin-slow"></div>
      </div>

      {/* Mobile-optimized container */}
      <div className="min-h-screen flex flex-col relative z-10">
        {/* Mobile-first header */}
        <motion.div
          className="px-4 pt-4 pb-2 sm:px-6 sm:pt-8 sm:pb-4 lg:px-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Back button - mobile optimized */}
          <motion.button
            onClick={() => navigate(-1)}
            className="mb-4 sm:mb-6 flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 transition-colors group p-2 -ml-2 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-950/20"
            whileHover={{ x: -4 }}
            whileTap={{ scale: 0.95 }}
          >
            <ArrowLeft className="w-5 h-5 mr-2 group-hover:animate-pulse" />
            <span className="font-medium">Back</span>
          </motion.button>

          {/* Mobile-optimized header */}
          <div className="mb-4 sm:mb-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-2 sm:mb-0 leading-tight">
                Get Your Custom Quote
              </h1>
              <div className="flex items-center">
                <div className="text-sm font-medium text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-full">
                  Step {currentStep} of {totalSteps}
                </div>
              </div>
            </div>

            {/* Mobile-optimized progress bar */}
            <div className="relative">
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 sm:h-2 overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full relative"
                  initial={{ width: 0 }}
                  animate={{ width: `${(currentStep / totalSteps) * 100}%` }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                >
                  {/* Progress indicator dot */}
                  <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-4 h-4 sm:w-3 sm:h-3 bg-primary-600 rounded-full border-2 border-white dark:border-gray-900 shadow-lg"></div>
                </motion.div>
              </div>

              {/* Step indicators for mobile */}
              <div className="flex justify-between mt-2 sm:hidden">
                {Array.from({ length: totalSteps }, (_, i) => (
                  <div
                    key={i}
                    className={`text-xs font-medium px-2 py-1 rounded ${
                      i + 1 <= currentStep
                        ? 'text-primary-600 dark:text-primary-400'
                        : 'text-gray-400 dark:text-gray-600'
                    }`}
                  >
                    {i + 1 === 1 && 'Info'}
                    {i + 1 === 2 && 'Service'}
                    {i + 1 === 3 && 'Details'}
                    {i + 1 === 4 && 'Review'}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>

        {/* Mobile-optimized main form container */}
        <div className="flex-1 px-4 sm:px-6 lg:px-8 pb-4 sm:pb-8">
          <motion.div
            ref={formRef}
            className="max-w-4xl mx-auto bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl rounded-2xl sm:rounded-3xl shadow-xl sm:shadow-2xl border border-white/20 dark:border-gray-700/30 overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <form onSubmit={handleSubmit} className="relative h-full flex flex-col">
              {/* Step content - mobile optimized */}
              <div className="flex-1 p-4 sm:p-6 lg:p-8">
                <AnimatePresence mode="wait">
                  {currentStep === 1 && (
                    <motion.div
                      key="step1"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.3 }}
                      className="h-full"
                    >
                      {/* Step 1: Personal Information - Mobile First */}
                      <div className="mb-6 sm:mb-8">
                        {/* Mobile-optimized step header */}
                        <div className="flex items-start sm:items-center mb-6 sm:mb-8">
                          <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0">
                            <User className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                          </div>
                          <div className="min-w-0 flex-1">
                            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white leading-tight">Personal Information</h2>
                            <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 mt-1">Tell us about yourself</p>
                          </div>
                        </div>

                        {/* Mobile-first form grid */}
                        <div className="space-y-4 sm:space-y-6">
                          {/* Full width on mobile, grid on larger screens */}
                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                            {/* Name field - Mobile optimized */}
                            <div className="relative">
                              <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                Full Name *
                              </label>
                              <div className="relative">
                                <input
                                  type="text"
                                  value={formData.name}
                                  onChange={(e) => handleInputChange('name', e.target.value)}
                                  className={`w-full px-4 py-4 sm:py-3 pl-12 rounded-xl border-2 transition-all duration-200 bg-white/70 dark:bg-gray-700/70 text-base sm:text-sm ${
                                    errors.name
                                      ? 'border-error-500 focus:border-error-600'
                                      : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                                  } focus:ring-2 focus:ring-primary-500/20 focus:outline-none`}
                                  placeholder="Enter your full name"
                                />
                                <User className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                              </div>
                              {errors.name && (
                                <motion.p
                                  initial={{ opacity: 0, y: -10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                                >
                                  <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                                  {errors.name}
                                </motion.p>
                              )}
                            </div>

                            {/* Email field - Mobile optimized */}
                            <div className="relative">
                              <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                Email Address *
                              </label>
                              <div className="relative">
                                <input
                                  type="email"
                                  value={formData.email}
                                  onChange={(e) => handleInputChange('email', e.target.value)}
                                  className={`w-full px-4 py-4 sm:py-3 pl-12 rounded-xl border-2 transition-all duration-200 bg-white/70 dark:bg-gray-700/70 text-base sm:text-sm ${
                                    errors.email
                                      ? 'border-error-500 focus:border-error-600'
                                      : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                                  } focus:ring-2 focus:ring-primary-500/20 focus:outline-none`}
                                  placeholder="Enter your email address"
                                />
                                <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                              </div>
                              {errors.email && (
                                <motion.p
                                  initial={{ opacity: 0, y: -10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                                >
                                  <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                                  {errors.email}
                                </motion.p>
                              )}
                            </div>

                            {/* Phone field - Mobile optimized */}
                            <div className="relative">
                              <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                Phone Number *
                              </label>
                              <div className="relative">
                                <input
                                  type="tel"
                                  value={formData.phone}
                                  onChange={(e) => handleInputChange('phone', e.target.value)}
                                  className={`w-full px-4 py-4 sm:py-3 pl-12 rounded-xl border-2 transition-all duration-200 bg-white/70 dark:bg-gray-700/70 text-base sm:text-sm ${
                                    errors.phone
                                      ? 'border-error-500 focus:border-error-600'
                                      : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                                  } focus:ring-2 focus:ring-primary-500/20 focus:outline-none`}
                                  placeholder="+****************"
                                />
                                <Phone className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                              </div>
                              {errors.phone && (
                                <motion.p
                                  initial={{ opacity: 0, y: -10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                                >
                                  <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                                  {errors.phone}
                                </motion.p>
                              )}
                            </div>

                            {/* Company field - Mobile optimized */}
                            <div className="relative">
                              <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                Company (Optional)
                              </label>
                              <input
                                type="text"
                                value={formData.company}
                                onChange={(e) => handleInputChange('company', e.target.value)}
                                className="w-full px-4 py-4 sm:py-3 rounded-xl border-2 border-gray-300 dark:border-gray-600 focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 transition-all duration-200 bg-white/70 dark:bg-gray-700/70 text-base sm:text-sm focus:outline-none"
                                placeholder="Your company name"
                              />
                            </div>
                          </div>

                          {/* Mobile-specific help text */}
                          <div className="mt-6 p-4 bg-primary-50 dark:bg-primary-950/20 rounded-xl border border-primary-200 dark:border-primary-800 sm:hidden">
                            <div className="flex items-start">
                              <div className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2 flex-shrink-0 mt-0.5">
                                <svg fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                </svg>
                              </div>
                              <div>
                                <p className="text-sm font-medium text-primary-800 dark:text-primary-200">Quick Tip</p>
                                <p className="text-xs text-primary-700 dark:text-primary-300 mt-1">
                                  We'll use this information to personalize your quote and contact you with updates.
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}

                {currentStep === 2 && (
                  <motion.div
                    key="step2"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                    className="h-full"
                  >
                    {/* Step 2: Service Selection - Mobile First */}
                    <div className="mb-6 sm:mb-8">
                      {/* Mobile-optimized step header */}
                      <div className="flex items-start sm:items-center mb-6 sm:mb-8">
                        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-secondary-500 to-accent-500 rounded-full flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0">
                          <Settings className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white leading-tight">Service Selection</h2>
                          <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 mt-1">Choose the service that best fits your needs</p>
                        </div>
                      </div>

                      {/* Mobile-optimized service options */}
                      <div className="space-y-3 sm:space-y-4 lg:grid lg:grid-cols-2 lg:gap-4 lg:space-y-0 mb-6 sm:mb-8">
                        {serviceOptions.map((service) => (
                          <motion.div
                            key={service.id}
                            className={`relative p-4 sm:p-6 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                              formData.service === service.id
                                ? 'border-primary-500 bg-primary-50 dark:bg-primary-950/20 shadow-lg'
                                : 'border-gray-300 dark:border-gray-600 hover:border-primary-300 bg-white/70 dark:bg-gray-700/70 hover:shadow-md'
                            }`}
                            onClick={() => handleInputChange('service', service.id)}
                            whileHover={{ scale: 1.01 }}
                            whileTap={{ scale: 0.99 }}
                          >
                            <div className="flex items-start space-x-3 sm:space-x-4">
                              <div className={`p-2 sm:p-3 rounded-lg flex-shrink-0 ${
                                formData.service === service.id
                                  ? 'bg-primary-500 text-white'
                                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                              }`}>
                                {service.icon}
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-start justify-between">
                                  <div className="flex-1 min-w-0">
                                    <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-1 sm:mb-2">
                                      {service.name}
                                    </h3>
                                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2 sm:mb-3 leading-relaxed">
                                      {service.description}
                                    </p>
                                    <div className="text-sm text-gray-500 dark:text-gray-500">
                                      Starting from <span className="font-semibold text-primary-600 dark:text-primary-400 text-base">
                                        ${service.basePrice.toLocaleString()}
                                      </span>
                                    </div>
                                  </div>
                                  {formData.service === service.id && (
                                    <motion.div
                                      initial={{ scale: 0 }}
                                      animate={{ scale: 1 }}
                                      className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center ml-2 flex-shrink-0"
                                    >
                                      <CheckCircle2 className="w-4 h-4 text-white" />
                                    </motion.div>
                                  )}
                                </div>

                                {/* Mobile-specific features list */}
                                {formData.service === service.id && (
                                  <motion.div
                                    initial={{ opacity: 0, height: 0 }}
                                    animate={{ opacity: 1, height: 'auto' }}
                                    className="mt-3 pt-3 border-t border-primary-200 dark:border-primary-800 sm:hidden"
                                  >
                                    <p className="text-xs font-medium text-primary-700 dark:text-primary-300 mb-2">Includes:</p>
                                    <ul className="space-y-1">
                                      {service.features.map((feature, index) => (
                                        <li key={index} className="text-xs text-primary-600 dark:text-primary-400 flex items-center">
                                          <div className="w-1 h-1 bg-primary-500 rounded-full mr-2 flex-shrink-0"></div>
                                          {feature}
                                        </li>
                                      ))}
                                    </ul>
                                  </motion.div>
                                )}
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>

                      {errors.service && (
                        <motion.p
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mb-4 sm:mb-6 text-sm text-error-600 dark:text-error-400 flex items-center bg-error-50 dark:bg-error-950/20 p-3 rounded-lg border border-error-200 dark:border-error-800"
                        >
                          <AlertCircle className="w-4 h-4 mr-2 flex-shrink-0" />
                          {errors.service}
                        </motion.p>
                      )}

                      {/* Mobile-optimized additional project details */}
                      <div className="space-y-4 sm:space-y-6 lg:grid lg:grid-cols-2 lg:gap-6 lg:space-y-0">
                        {/* Project Type */}
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                            Project Type *
                          </label>
                          <select
                            value={formData.projectType}
                            onChange={(e) => handleInputChange('projectType', e.target.value)}
                            className={`w-full px-4 py-4 sm:py-3 rounded-xl border-2 transition-all duration-200 bg-white/70 dark:bg-gray-700/70 text-base sm:text-sm ${
                              errors.projectType
                                ? 'border-error-500 focus:border-error-600'
                                : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                            } focus:ring-2 focus:ring-primary-500/20 focus:outline-none appearance-none`}
                          >
                            <option value="">Select project type</option>
                            <option value="new-installation">New Installation</option>
                            <option value="upgrade">System Upgrade</option>
                            <option value="maintenance">Maintenance Contract</option>
                            <option value="consultation">Consultation Only</option>
                            <option value="emergency">Emergency Service</option>
                          </select>
                          {errors.projectType && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                            >
                              <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                              {errors.projectType}
                            </motion.p>
                          )}
                        </div>

                        {/* Timeline */}
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                            Timeline *
                          </label>
                          <select
                            value={formData.timeline}
                            onChange={(e) => handleInputChange('timeline', e.target.value)}
                            className={`w-full px-4 py-4 sm:py-3 rounded-xl border-2 transition-all duration-200 bg-white/70 dark:bg-gray-700/70 text-base sm:text-sm ${
                              errors.timeline
                                ? 'border-error-500 focus:border-error-600'
                                : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                            } focus:ring-2 focus:ring-primary-500/20 focus:outline-none appearance-none`}
                          >
                            <option value="">Select timeline</option>
                            <option value="urgent">ASAP (Rush Job)</option>
                            <option value="1-month">Within 1 Month</option>
                            <option value="3-months">Within 3 Months</option>
                            <option value="6-months">Within 6 Months</option>
                            <option value="flexible">Flexible Timeline</option>
                          </select>
                          {errors.timeline && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                            >
                              <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                              {errors.timeline}
                            </motion.p>
                          )}
                        </div>

                        {/* Budget - Full width on mobile */}
                        <div className="lg:col-span-2">
                          <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                            Budget Range *
                          </label>
                          <select
                            value={formData.budget}
                            onChange={(e) => handleInputChange('budget', e.target.value)}
                            className={`w-full px-4 py-4 sm:py-3 rounded-xl border-2 transition-all duration-200 bg-white/70 dark:bg-gray-700/70 text-base sm:text-sm ${
                              errors.budget
                                ? 'border-error-500 focus:border-error-600'
                                : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                            } focus:ring-2 focus:ring-primary-500/20 focus:outline-none appearance-none`}
                          >
                            <option value="">Select budget range</option>
                            <option value="under-10k">Under $10,000</option>
                            <option value="10k-25k">$10,000 - $25,000</option>
                            <option value="25k-50k">$25,000 - $50,000</option>
                            <option value="50k-100k">$50,000 - $100,000</option>
                            <option value="over-100k">Over $100,000</option>
                          </select>
                          {errors.budget && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                            >
                              <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                              {errors.budget}
                            </motion.p>
                          )}
                        </div>
                      </div>

                      {/* Mobile-optimized estimated cost display */}
                      {estimatedCost > 0 && (
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mt-6 p-4 bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-950/20 dark:to-secondary-950/20 rounded-xl border border-primary-200 dark:border-primary-800"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <Calculator className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2 flex-shrink-0" />
                              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Estimated Cost:</span>
                            </div>
                            <span className="text-lg sm:text-xl font-bold text-primary-600 dark:text-primary-400">
                              ${estimatedCost.toLocaleString()}
                            </span>
                          </div>
                          <p className="text-xs text-gray-500 dark:text-gray-500 mt-2 leading-relaxed">
                            *This is a preliminary estimate. Final pricing will be provided after consultation.
                          </p>
                        </motion.div>
                      )}
                    </div>
                  </motion.div>
                )}

                {/* Simplified Steps 3 & 4 for mobile - keeping existing structure but with mobile optimizations */}
                {(currentStep === 3 || currentStep === 4) && (
                  <motion.div
                    key={`step${currentStep}`}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                    className="h-full"
                  >
                    <div className="text-center py-8 sm:py-12">
                      <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-r from-accent-500 to-success-500 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                        <FileText className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
                      </div>
                      <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-2 sm:mb-4">
                        {currentStep === 3 ? 'Project Requirements' : 'Review & Submit'}
                      </h2>
                      <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 mb-6 sm:mb-8 max-w-md mx-auto leading-relaxed">
                        {currentStep === 3
                          ? 'Tell us more about your specific requirements and project details.'
                          : 'Review your information and submit your quote request.'
                        }
                      </p>

                      {currentStep === 3 && (
                        <div className="text-left max-w-lg mx-auto">
                          <textarea
                            value={formData.description}
                            onChange={(e) => handleInputChange('description', e.target.value)}
                            rows={6}
                            className="w-full px-4 py-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 transition-all duration-200 bg-white/70 dark:bg-gray-700/70 text-base resize-none focus:outline-none"
                            placeholder="Please describe your project requirements, goals, and any specific needs in detail..."
                          />
                          {errors.description && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                            >
                              <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                              {errors.description}
                            </motion.p>
                          )}
                        </div>
                      )}

                      {currentStep === 4 && (
                        <div className="bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-950/20 dark:to-secondary-950/20 rounded-xl p-4 sm:p-6 border border-primary-200 dark:border-primary-800 max-w-lg mx-auto">
                          <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center justify-center">
                            <CheckCircle2 className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2" />
                            Quote Summary
                          </h3>
                          <div className="space-y-3 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600 dark:text-gray-400">Service:</span>
                              <span className="font-medium text-gray-900 dark:text-white">
                                {serviceOptions.find(s => s.id === formData.service)?.name || 'Not selected'}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600 dark:text-gray-400">Timeline:</span>
                              <span className="font-medium text-gray-900 dark:text-white">
                                {formData.timeline.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Not selected'}
                              </span>
                            </div>
                            <div className="flex justify-between pt-2 border-t border-primary-200 dark:border-primary-800">
                              <span className="text-gray-600 dark:text-gray-400">Estimated Cost:</span>
                              <span className="font-bold text-primary-600 dark:text-primary-400">
                                ${estimatedCost.toLocaleString()}
                              </span>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Mobile-optimized navigation buttons */}
            <div className="p-4 sm:p-6 lg:p-8 bg-gray-50/80 dark:bg-gray-900/80 border-t border-gray-200 dark:border-gray-700">
              <div className="flex justify-between items-center max-w-4xl mx-auto">
                <motion.button
                  type="button"
                  onClick={prevStep}
                  disabled={currentStep === 1}
                  className={`flex items-center px-4 sm:px-6 py-3 sm:py-3 rounded-xl font-medium transition-all duration-200 ${
                    currentStep === 1
                      ? 'text-gray-400 cursor-not-allowed'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 active:scale-95'
                  }`}
                  whileHover={currentStep > 1 ? { x: -4 } : {}}
                  whileTap={currentStep > 1 ? { scale: 0.95 } : {}}
                >
                  <ArrowLeft className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                  <span className="text-sm sm:text-base">Previous</span>
                </motion.button>

                {currentStep < totalSteps ? (
                  <motion.button
                    type="button"
                    onClick={nextStep}
                    className="flex items-center px-6 sm:px-8 py-3 sm:py-3 bg-gradient-to-r from-primary-500 to-secondary-500 text-white font-semibold rounded-xl hover:from-primary-600 hover:to-secondary-600 transition-all duration-300 shadow-lg hover:shadow-xl text-sm sm:text-base"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <span>Next Step</span>
                    <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 ml-2" />
                  </motion.button>
                ) : (
                  <motion.button
                    type="submit"
                    disabled={isSubmitting}
                    className="flex items-center px-6 sm:px-8 py-3 sm:py-3 bg-gradient-to-r from-success-500 to-accent-500 text-white font-semibold rounded-xl hover:from-success-600 hover:to-accent-600 transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base"
                    whileHover={!isSubmitting ? { scale: 1.02 } : {}}
                    whileTap={!isSubmitting ? { scale: 0.98 } : {}}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                        <span>Submitting...</span>
                      </>
                    ) : (
                      <>
                        <span>Submit Request</span>
                        <CheckCircle2 className="w-4 h-4 sm:w-5 sm:h-5 ml-2" />
                      </>
                    )}
                  </motion.button>
                )}
              </div>
            </div>
          </form>
        </motion.div>
        </div>
      </div>
    </div>
  );
};

export default QuoteRequest;
