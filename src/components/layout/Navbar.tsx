import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useTheme } from '../../context/ThemeContext';
import { Menu, <PERSON>, <PERSON>, Sun, Facebook, Linkedin, Phone } from 'lucide-react';
import { AnimatePresence } from 'framer-motion';

const Navbar: React.FC = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { theme, toggleTheme } = useTheme();
  const [hoveredIdx, setHoveredIdx] = useState<number | null>(null);
  const underlineRef = useRef<HTMLDivElement>(null);
  const navRefs = useRef<(HTMLAnchorElement | null)[]>([]);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const navLinks = [
    { name: 'Home', href: '/' },
    { name: 'About', href: '#about' },
    { name: 'Services', href: '#services' },
    { name: 'Projects', href: '/projects' },
    { name: 'Gallery', href: '/gallery' },
    { name: 'Contact', href: '#contact' },
  ];

  useEffect(() => {
    if (hoveredIdx !== null && navRefs.current[hoveredIdx] && underlineRef.current) {
      const el = navRefs.current[hoveredIdx];
      const rect = el!.getBoundingClientRect();
      underlineRef.current.style.width = rect.width + 'px';
      underlineRef.current.style.left = el!.offsetLeft + 'px';
      underlineRef.current.style.opacity = '1';
    } else if (underlineRef.current) {
      underlineRef.current.style.opacity = '0';
    }
  }, [hoveredIdx]);

  const handleNavClick = (href: string, idx: number) => (e: React.MouseEvent) => {
    if (href.startsWith('#')) {
      e.preventDefault();
      const sectionId = href.replace('#', '');
      if (location.pathname !== '/') {
        navigate('/', { replace: false });
        // Wait for navigation, then scroll
        setTimeout(() => {
          const el = document.getElementById(sectionId);
          if (el) {
            el.scrollIntoView({ behavior: 'smooth' });
          }
        }, 100); // Delay to allow page to render
      } else {
        const el = document.getElementById(sectionId);
        if (el) {
          el.scrollIntoView({ behavior: 'smooth' });
        }
      }
      setHoveredIdx(idx);
    }
    // else let Link or <a> handle
  };

  return (
    <nav
      className={`fixed top-0 w-full z-50 transition-all duration-300 bg-white dark:bg-gray-900 shadow-xl overflow-visible ${
        isScrolled ? 'py-1' : 'py-3'
      }`}
      style={{ borderBottom: '1.5px solid rgba(180,180,200,0.13)' }}
    >
      {/* Navbar Content */}
      <div className="container mx-auto px-4 md:px-8 relative">
        <div className="flex justify-between items-center">
          <div className="w-48"> {/* This div maintains space for the logo */}
            <motion.a 
              href="#home"
              className="absolute left-0 top-0 -translate-y-1/4 text-primary-600 dark:text-primary-400 group select-none bg-white dark:bg-gray-900 rounded-2xl p-2 z-10"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              <img
                src="/logoPazogen.png"
                alt="Pazogen Water & Wastewater Engineering Logo"
                className="h-40 w-auto object-contain drop-shadow-xl"
                style={{ 
                  filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))',
                  transform: 'translateY(10px)'
                }}
              />
            </motion.a>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6 xl:space-x-8">
            <ul className="flex space-x-7 relative">
              <div
                ref={underlineRef}
                className="absolute bottom-0 h-1 rounded-full bg-gradient-to-r from-primary-500 via-blue-400 to-cyan-400 dark:from-primary-400 dark:via-blue-300 dark:to-cyan-300 transition-all duration-300 pointer-events-none"
                style={{ left: 0, width: 0, opacity: 0, zIndex: 0 }}
              />
              {navLinks.map((link, idx) => (
                <li key={link.name} className="relative">
                  {link.href.startsWith('/') ? (
                    <Link
                      ref={el => (navRefs.current[idx] = el)}
                      to={link.href}
                      className="text-gray-700 dark:text-gray-200 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 font-semibold px-2 py-1 rounded-lg focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-400"
                      onMouseEnter={() => setHoveredIdx(idx)}
                      onMouseLeave={() => setHoveredIdx(null)}
                    >
                      {link.name}
                    </Link>
                  ) : (
                    <a
                      ref={el => (navRefs.current[idx] = el)}
                      href={link.href}
                      className="text-gray-700 dark:text-gray-200 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 font-semibold px-2 py-1 rounded-lg focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-400"
                      onMouseEnter={() => setHoveredIdx(idx)}
                      onMouseLeave={() => setHoveredIdx(null)}
                      onClick={handleNavClick(link.href, idx)}
                    >
                      {link.name}
                    </a>
                  )}
                </li>
              ))}
            </ul>
            {/* Social Icons - Desktop */}
            <div className="flex items-center space-x-3 xl:space-x-4">
              <a 
                href="https://www.facebook.com/share/16QSfYDVAB/?mibextid=wwXIfr" 
                target="_blank" 
                rel="noopener noreferrer" 
                aria-label="Facebook"
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200"
              >
                <Facebook size={22} />
              </a>
              <a 
                href="https://www.linkedin.com/company/pazogen-water-wastewater-engineering/" 
                target="_blank" 
                rel="noopener noreferrer" 
                aria-label="LinkedIn"
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200"
              >
                <Linkedin size={22} />
              </a>
            </div>

            {/* Call Us Button - Desktop */}
            <a
              href="tel:0101096528"
              className="ml-4 flex items-center bg-blue-600 hover:bg-blue-700 text-white font-semibold px-3 py-2 xl:px-4 rounded-full transition-colors duration-200 text-xs xl:text-sm shadow-md whitespace-nowrap"
            >
              <Phone size={16} className="mr-1 xl:mr-2" />
              Call us: ************
            </a>

            {/* Theme Toggle Button */}
            <motion.button
              onClick={toggleTheme}
              className={`ml-8 flex items-center bg-gray-100/70 dark:bg-gray-800/70 rounded-full px-1.5 py-1 xl:px-2 shadow-inner border-0 transition-all duration-200 relative w-12 h-7 xl:w-14 xl:h-8 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-400`}
              whileTap={{ scale: 0.95 }}
              aria-label={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
            >
              <motion.span
                className="block w-5 h-5 xl:w-6 xl:h-6 bg-white dark:bg-gray-700 rounded-full shadow-md flex items-center justify-center"
                animate={{ x: theme === 'dark' ? 28 : 0 }}
                transition={{ type: 'spring', stiffness: 300, damping: 20 }}
              >
                {theme === 'dark' ? <Sun size={18} className="text-yellow-400" /> : <Moon size={18} className="text-blue-500" />}
              </motion.span>
            </motion.button>
          </div>

          {/* Mobile Navigation Toggle */}
          <div className="flex items-center md:hidden space-x-2">
            <motion.button
              onClick={toggleTheme}
              className="p-2 rounded-full bg-gray-100/80 dark:bg-gray-800/80 shadow border-0 text-gray-700 dark:text-gray-200 hover:bg-primary-100 dark:hover:bg-primary-900 transition-all duration-200"
              whileTap={{ scale: 0.92 }}
              aria-label={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
            >
              {theme === 'dark' ? <Sun size={20} /> : <Moon size={20} />}
            </motion.button>
            <motion.button
              onClick={toggleMenu}
              className="p-2 rounded-full bg-gray-100/80 dark:bg-gray-800/80 shadow border-0 text-gray-700 dark:text-gray-200 hover:bg-primary-100 dark:hover:bg-primary-900 transition-all duration-200"
              whileTap={{ scale: 0.92 }}
              aria-label="Menu"
            >
              {isMenuOpen ? <X size={26} /> : <Menu size={26} />}
            </motion.button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="md:hidden absolute top-full left-0 right-0 bg-white dark:bg-gray-900 shadow-lg py-4"
            style={{ borderBottom: '1.5px solid rgba(180,180,200,0.13)', borderTop: '1.5px solid rgba(180,180,200,0.13)' }}
          >
            <ul className="flex flex-col items-center space-y-4">
              {navLinks.map((link) => (
                <li key={link.name}>
                  {link.href.startsWith('/') ? (
                    <Link
                      to={link.href}
                      className="text-gray-700 dark:text-gray-200 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 font-semibold text-lg"
                      onClick={toggleMenu}
                    >
                      {link.name}
                    </Link>
                  ) : (
                    <a
                      href={link.href}
                      className="text-gray-700 dark:text-gray-200 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 font-semibold text-lg"
                      onClick={(e) => { 
                        handleNavClick(link.href, navLinks.findIndex(nl => nl.name === link.name))(e);
                        toggleMenu(); 
                      }}
                    >
                      {link.name}
                    </a>
                  )}
                </li>
              ))}
            </ul>
            {/* Social Icons - Mobile Menu */}
            <div className="flex justify-center items-center space-x-6 pt-6 mt-4 border-t border-gray-200 dark:border-gray-700">
              <a 
                href="#facebook" 
                target="_blank" 
                rel="noopener noreferrer" 
                aria-label="Facebook"
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200"
              >
                <Facebook size={24} />
              </a>
              <a 
                href="#linkedin" 
                target="_blank" 
                rel="noopener noreferrer" 
                aria-label="LinkedIn"
                className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200"
              >
                <Linkedin size={24} />
              </a>
            </div>

            {/* Call Us Button - Mobile Menu */}
            <div className="mt-6 px-4">
              <a
                href="tel:0101096528"
                className="w-full flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-semibold px-4 py-3 rounded-full transition-colors duration-200 shadow-md text-base"
              >
                <Phone size={18} className="mr-2" />
                Call us: ************
              </a>
            </div>

          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  );
};

export default Navbar;