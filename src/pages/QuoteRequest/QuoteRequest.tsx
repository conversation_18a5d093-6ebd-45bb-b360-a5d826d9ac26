import React, { useEffect, useRef, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft,
  ArrowRight,
  Check,
  Upload,
  Calculator,
  Clock,
  DollarSign,
  User,
  Mail,
  Phone,
  MessageSquare,
  FileText,
  Zap,
  Droplets,
  Settings,
  Shield,
  AlertCircle,
  CheckCircle2
} from 'lucide-react';
import gsap from 'gsap';
import { useNavigate } from 'react-router-dom';

// Types for form data
interface FormData {
  // Step 1: Personal Info
  name: string;
  email: string;
  phone: string;
  company?: string;

  // Step 2: Project Details
  service: string;
  projectType: string;
  timeline: string;
  budget: string;

  // Step 3: Requirements
  description: string;
  requirements: string[];
  files: File[];

  // Step 4: Additional Info
  urgency: string;
  preferredContact: string;
  additionalNotes?: string;
}

interface FormErrors {
  [key: string]: string;
}

interface ServiceOption {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  basePrice: number;
  features: string[];
}

const QuoteRequest: React.FC = () => {
  const formRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  // Form state
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    service: '',
    projectType: '',
    timeline: '',
    budget: '',
    description: '',
    requirements: [],
    files: [],
    urgency: '',
    preferredContact: '',
    additionalNotes: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [estimatedCost, setEstimatedCost] = useState(0);

  // Service options
  const serviceOptions: ServiceOption[] = [
    {
      id: 'water-treatment',
      name: 'Water Treatment Solutions',
      description: 'Advanced filtration and purification systems',
      icon: <Droplets className="w-8 h-8" />,
      basePrice: 5000,
      features: ['Custom filtration', 'Quality monitoring', '24/7 support']
    },
    {
      id: 'consulting',
      name: 'Consulting Services',
      description: 'Expert guidance and strategic planning',
      icon: <Settings className="w-8 h-8" />,
      basePrice: 2500,
      features: ['Strategic planning', 'Process optimization', 'Compliance guidance']
    },
    {
      id: 'maintenance',
      name: 'Maintenance & Support',
      description: 'Ongoing system maintenance and support',
      icon: <Shield className="w-8 h-8" />,
      basePrice: 1500,
      features: ['Regular maintenance', 'Emergency support', 'Performance monitoring']
    },
    {
      id: 'custom',
      name: 'Custom Solutions',
      description: 'Tailored solutions for unique requirements',
      icon: <Zap className="w-8 h-8" />,
      basePrice: 7500,
      features: ['Custom development', 'Integration services', 'Specialized equipment']
    }
  ];

  const totalSteps = 4;

  // Form validation
  const validateStep = useCallback((step: number): boolean => {
    const newErrors: FormErrors = {};

    switch (step) {
      case 1:
        if (!formData.name.trim()) newErrors.name = 'Name is required';
        if (!formData.email.trim()) {
          newErrors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
          newErrors.email = 'Please enter a valid email';
        }
        if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';
        break;

      case 2:
        if (!formData.service) newErrors.service = 'Please select a service';
        if (!formData.projectType) newErrors.projectType = 'Please specify project type';
        if (!formData.timeline) newErrors.timeline = 'Please select a timeline';
        if (!formData.budget) newErrors.budget = 'Please select a budget range';
        break;

      case 3:
        if (!formData.description.trim()) newErrors.description = 'Project description is required';
        if (formData.requirements.length === 0) newErrors.requirements = 'Please select at least one requirement';
        break;

      case 4:
        if (!formData.urgency) newErrors.urgency = 'Please specify urgency level';
        if (!formData.preferredContact) newErrors.preferredContact = 'Please select preferred contact method';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Calculate estimated cost
  const calculateEstimatedCost = useCallback(() => {
    const selectedService = serviceOptions.find(s => s.id === formData.service);
    if (!selectedService) return 0;

    let cost = selectedService.basePrice;

    // Timeline multiplier
    const timelineMultipliers: { [key: string]: number } = {
      'urgent': 1.5,
      '1-month': 1.2,
      '3-months': 1.0,
      '6-months': 0.9,
      'flexible': 0.8
    };

    // Budget adjustments
    const budgetMultipliers: { [key: string]: number } = {
      'under-10k': 0.8,
      '10k-25k': 1.0,
      '25k-50k': 1.2,
      '50k-100k': 1.5,
      'over-100k': 2.0
    };

    cost *= timelineMultipliers[formData.timeline] || 1;
    cost *= budgetMultipliers[formData.budget] || 1;

    // Requirements multiplier
    cost *= (1 + formData.requirements.length * 0.1);

    return Math.round(cost);
  }, [formData, serviceOptions]);

  // Update estimated cost when relevant fields change
  useEffect(() => {
    setEstimatedCost(calculateEstimatedCost());
  }, [formData.service, formData.timeline, formData.budget, formData.requirements, calculateEstimatedCost]);

  // Handle form field changes
  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Handle step navigation
  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateStep(currentStep)) return;

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Show success message or redirect
      console.log('Form submitted:', formData);

      // Reset form or redirect to success page
      // navigate('/quote-success');

    } catch (error) {
      console.error('Submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Animate form entrance
  useEffect(() => {
    if (formRef.current) {
      gsap.fromTo(
        formRef.current.children,
        { y: 20, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 0.6,
          stagger: 0.1,
          ease: 'power3.out',
        }
      );
    }
  }, [currentStep]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-cyan-50 to-teal-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-8 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-cyan-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-teal-400/20 to-blue-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-cyan-400/10 to-teal-400/10 rounded-full blur-3xl animate-spin-slow"></div>
      </div>

      <div className="max-w-5xl mx-auto relative z-10">
        {/* Header */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <motion.button
            onClick={() => navigate(-1)}
            className="mb-6 flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 transition-colors group"
            whileHover={{ x: -4 }}
          >
            <ArrowLeft className="w-5 h-5 mr-2 group-hover:animate-pulse" />
            Back to Home
          </motion.button>

          {/* Progress indicator */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white">
                Get Your Custom Quote
              </h1>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Step {currentStep} of {totalSteps}
              </div>
            </div>

            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
              <motion.div
                className="h-full bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${(currentStep / totalSteps) * 100}%` }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
              />
            </div>
          </div>
        </motion.div>

        {/* Main form container */}
        <motion.div
          ref={formRef}
          className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 dark:border-gray-700/30 overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <form onSubmit={handleSubmit} className="relative">
            {/* Step content */}
            <div className="p-8 sm:p-12">
              <AnimatePresence mode="wait">
                {currentStep === 1 && (
                  <motion.div
                    key="step1"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    {/* Step 1: Personal Information */}
                    <div className="mb-8">
                      <div className="flex items-center mb-6">
                        <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center mr-4">
                          <User className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Personal Information</h2>
                          <p className="text-gray-600 dark:text-gray-400">Tell us about yourself</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Name field */}
                        <div className="relative">
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Full Name *
                          </label>
                          <div className="relative">
                            <input
                              type="text"
                              value={formData.name}
                              onChange={(e) => handleInputChange('name', e.target.value)}
                              className={`w-full px-4 py-3 pl-12 rounded-xl border-2 transition-all duration-200 bg-white/50 dark:bg-gray-700/50 ${
                                errors.name
                                  ? 'border-error-500 focus:border-error-600'
                                  : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                              } focus:ring-2 focus:ring-primary-500/20`}
                              placeholder="Enter your full name"
                            />
                            <User className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                          </div>
                          {errors.name && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                            >
                              <AlertCircle className="w-4 h-4 mr-1" />
                              {errors.name}
                            </motion.p>
                          )}
                        </div>

                        {/* Email field */}
                        <div className="relative">
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Email Address *
                          </label>
                          <div className="relative">
                            <input
                              type="email"
                              value={formData.email}
                              onChange={(e) => handleInputChange('email', e.target.value)}
                              className={`w-full px-4 py-3 pl-12 rounded-xl border-2 transition-all duration-200 bg-white/50 dark:bg-gray-700/50 ${
                                errors.email
                                  ? 'border-error-500 focus:border-error-600'
                                  : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                              } focus:ring-2 focus:ring-primary-500/20`}
                              placeholder="Enter your email address"
                            />
                            <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                          </div>
                          {errors.email && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                            >
                              <AlertCircle className="w-4 h-4 mr-1" />
                              {errors.email}
                            </motion.p>
                          )}
                        </div>

                        {/* Phone field */}
                        <div className="relative">
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Phone Number *
                          </label>
                          <div className="relative">
                            <input
                              type="tel"
                              value={formData.phone}
                              onChange={(e) => handleInputChange('phone', e.target.value)}
                              className={`w-full px-4 py-3 pl-12 rounded-xl border-2 transition-all duration-200 bg-white/50 dark:bg-gray-700/50 ${
                                errors.phone
                                  ? 'border-error-500 focus:border-error-600'
                                  : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                              } focus:ring-2 focus:ring-primary-500/20`}
                              placeholder="+****************"
                            />
                            <Phone className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                          </div>
                          {errors.phone && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                            >
                              <AlertCircle className="w-4 h-4 mr-1" />
                              {errors.phone}
                            </motion.p>
                          )}
                        </div>

                        {/* Company field (optional) */}
                        <div className="relative">
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Company (Optional)
                          </label>
                          <input
                            type="text"
                            value={formData.company}
                            onChange={(e) => handleInputChange('company', e.target.value)}
                            className="w-full px-4 py-3 rounded-xl border-2 border-gray-300 dark:border-gray-600 focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 transition-all duration-200 bg-white/50 dark:bg-gray-700/50"
                            placeholder="Your company name"
                          />
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}

                {currentStep === 2 && (
                  <motion.div
                    key="step2"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    {/* Step 2: Service Selection */}
                    <div className="mb-8">
                      <div className="flex items-center mb-6">
                        <div className="w-12 h-12 bg-gradient-to-r from-secondary-500 to-accent-500 rounded-full flex items-center justify-center mr-4">
                          <Settings className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Service Selection</h2>
                          <p className="text-gray-600 dark:text-gray-400">Choose the service that best fits your needs</p>
                        </div>
                      </div>

                      {/* Service options */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                        {serviceOptions.map((service) => (
                          <motion.div
                            key={service.id}
                            className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                              formData.service === service.id
                                ? 'border-primary-500 bg-primary-50 dark:bg-primary-950/20'
                                : 'border-gray-300 dark:border-gray-600 hover:border-primary-300 bg-white/50 dark:bg-gray-700/50'
                            }`}
                            onClick={() => handleInputChange('service', service.id)}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <div className="flex items-start space-x-4">
                              <div className={`p-3 rounded-lg ${
                                formData.service === service.id
                                  ? 'bg-primary-500 text-white'
                                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                              }`}>
                                {service.icon}
                              </div>
                              <div className="flex-1">
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                                  {service.name}
                                </h3>
                                <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">
                                  {service.description}
                                </p>
                                <div className="text-sm text-gray-500 dark:text-gray-500">
                                  Starting from <span className="font-semibold text-primary-600 dark:text-primary-400">
                                    ${service.basePrice.toLocaleString()}
                                  </span>
                                </div>
                              </div>
                              {formData.service === service.id && (
                                <motion.div
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center"
                                >
                                  <CheckCircle2 className="w-4 h-4 text-white" />
                                </motion.div>
                              )}
                            </div>
                          </motion.div>
                        ))}
                      </div>

                      {errors.service && (
                        <motion.p
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mb-6 text-sm text-error-600 dark:text-error-400 flex items-center"
                        >
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {errors.service}
                        </motion.p>
                      )}

                      {/* Additional project details */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Project Type */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Project Type *
                          </label>
                          <select
                            value={formData.projectType}
                            onChange={(e) => handleInputChange('projectType', e.target.value)}
                            className={`w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 bg-white/50 dark:bg-gray-700/50 ${
                              errors.projectType
                                ? 'border-error-500 focus:border-error-600'
                                : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                            } focus:ring-2 focus:ring-primary-500/20`}
                          >
                            <option value="">Select project type</option>
                            <option value="new-installation">New Installation</option>
                            <option value="upgrade">System Upgrade</option>
                            <option value="maintenance">Maintenance Contract</option>
                            <option value="consultation">Consultation Only</option>
                            <option value="emergency">Emergency Service</option>
                          </select>
                          {errors.projectType && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                            >
                              <AlertCircle className="w-4 h-4 mr-1" />
                              {errors.projectType}
                            </motion.p>
                          )}
                        </div>

                        {/* Timeline */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Timeline *
                          </label>
                          <select
                            value={formData.timeline}
                            onChange={(e) => handleInputChange('timeline', e.target.value)}
                            className={`w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 bg-white/50 dark:bg-gray-700/50 ${
                              errors.timeline
                                ? 'border-error-500 focus:border-error-600'
                                : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                            } focus:ring-2 focus:ring-primary-500/20`}
                          >
                            <option value="">Select timeline</option>
                            <option value="urgent">ASAP (Rush Job)</option>
                            <option value="1-month">Within 1 Month</option>
                            <option value="3-months">Within 3 Months</option>
                            <option value="6-months">Within 6 Months</option>
                            <option value="flexible">Flexible Timeline</option>
                          </select>
                          {errors.timeline && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                            >
                              <AlertCircle className="w-4 h-4 mr-1" />
                              {errors.timeline}
                            </motion.p>
                          )}
                        </div>

                        {/* Budget */}
                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Budget Range *
                          </label>
                          <select
                            value={formData.budget}
                            onChange={(e) => handleInputChange('budget', e.target.value)}
                            className={`w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 bg-white/50 dark:bg-gray-700/50 ${
                              errors.budget
                                ? 'border-error-500 focus:border-error-600'
                                : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                            } focus:ring-2 focus:ring-primary-500/20`}
                          >
                            <option value="">Select budget range</option>
                            <option value="under-10k">Under $10,000</option>
                            <option value="10k-25k">$10,000 - $25,000</option>
                            <option value="25k-50k">$25,000 - $50,000</option>
                            <option value="50k-100k">$50,000 - $100,000</option>
                            <option value="over-100k">Over $100,000</option>
                          </select>
                          {errors.budget && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                            >
                              <AlertCircle className="w-4 h-4 mr-1" />
                              {errors.budget}
                            </motion.p>
                          )}
                        </div>
                      </div>

                      {/* Estimated cost display */}
                      {estimatedCost > 0 && (
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mt-6 p-4 bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-950/20 dark:to-secondary-950/20 rounded-xl border border-primary-200 dark:border-primary-800"
                        >
                          <div className="flex items-center">
                            <Calculator className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2" />
                            <span className="text-sm text-gray-600 dark:text-gray-400">Estimated Project Cost:</span>
                            <span className="ml-2 text-lg font-bold text-primary-600 dark:text-primary-400">
                              ${estimatedCost.toLocaleString()}
                            </span>
                          </div>
                          <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                            *This is a preliminary estimate. Final pricing will be provided after consultation.
                          </p>
                        </motion.div>
                      )}
                    </div>
                  </motion.div>
                )}

                {currentStep === 3 && (
                  <motion.div
                    key="step3"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    {/* Step 3: Project Requirements */}
                    <div className="mb-8">
                      <div className="flex items-center mb-6">
                        <div className="w-12 h-12 bg-gradient-to-r from-accent-500 to-success-500 rounded-full flex items-center justify-center mr-4">
                          <FileText className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Project Requirements</h2>
                          <p className="text-gray-600 dark:text-gray-400">Describe your project in detail</p>
                        </div>
                      </div>

                      {/* Project Description */}
                      <div className="mb-6">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Project Description *
                        </label>
                        <div className="relative">
                          <textarea
                            value={formData.description}
                            onChange={(e) => handleInputChange('description', e.target.value)}
                            rows={5}
                            className={`w-full px-4 py-3 pl-12 rounded-xl border-2 transition-all duration-200 bg-white/50 dark:bg-gray-700/50 resize-none ${
                              errors.description
                                ? 'border-error-500 focus:border-error-600'
                                : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                            } focus:ring-2 focus:ring-primary-500/20`}
                            placeholder="Please describe your project requirements, goals, and any specific needs..."
                          />
                          <MessageSquare className="absolute left-4 top-4 w-5 h-5 text-gray-400" />
                        </div>
                        {errors.description && (
                          <motion.p
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                          >
                            <AlertCircle className="w-4 h-4 mr-1" />
                            {errors.description}
                          </motion.p>
                        )}
                      </div>

                      {/* Requirements Checklist */}
                      <div className="mb-6">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                          Specific Requirements *
                        </label>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {[
                            'Water quality testing',
                            'System installation',
                            'Maintenance plan',
                            'Emergency support',
                            'Compliance certification',
                            'Training services',
                            'Documentation',
                            'Remote monitoring'
                          ].map((requirement) => (
                            <motion.label
                              key={requirement}
                              className="flex items-center p-3 rounded-lg border border-gray-300 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-600 cursor-pointer transition-all duration-200 bg-white/50 dark:bg-gray-700/50"
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                            >
                              <input
                                type="checkbox"
                                checked={formData.requirements.includes(requirement)}
                                onChange={(e) => {
                                  const newRequirements = e.target.checked
                                    ? [...formData.requirements, requirement]
                                    : formData.requirements.filter(r => r !== requirement);
                                  handleInputChange('requirements', newRequirements);
                                }}
                                className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                              />
                              <span className="ml-3 text-sm text-gray-700 dark:text-gray-300">{requirement}</span>
                            </motion.label>
                          ))}
                        </div>
                        {errors.requirements && (
                          <motion.p
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                          >
                            <AlertCircle className="w-4 h-4 mr-1" />
                            {errors.requirements}
                          </motion.p>
                        )}
                      </div>

                      {/* File Upload */}
                      <div className="mb-6">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Supporting Documents (Optional)
                        </label>
                        <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl p-6 text-center hover:border-primary-400 dark:hover:border-primary-500 transition-colors duration-200 bg-white/30 dark:bg-gray-700/30">
                          <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                            Drop files here or click to browse
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-500">
                            Supported formats: PDF, DOC, DOCX, JPG, PNG (Max 10MB each)
                          </p>
                          <input
                            type="file"
                            multiple
                            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                            onChange={(e) => {
                              const files = Array.from(e.target.files || []);
                              handleInputChange('files', files);
                            }}
                            className="hidden"
                            id="file-upload"
                          />
                          <label
                            htmlFor="file-upload"
                            className="mt-3 inline-flex items-center px-4 py-2 bg-primary-500 text-white text-sm font-medium rounded-lg hover:bg-primary-600 transition-colors duration-200 cursor-pointer"
                          >
                            <Upload className="w-4 h-4 mr-2" />
                            Choose Files
                          </label>
                        </div>
                        {formData.files.length > 0 && (
                          <div className="mt-3 space-y-2">
                            {formData.files.map((file, index) => (
                              <div key={index} className="flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded-lg">
                                <span className="text-sm text-gray-700 dark:text-gray-300">{file.name}</span>
                                <button
                                  type="button"
                                  onClick={() => {
                                    const newFiles = formData.files.filter((_, i) => i !== index);
                                    handleInputChange('files', newFiles);
                                  }}
                                  className="text-error-500 hover:text-error-700 text-sm"
                                >
                                  Remove
                                </button>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                )}

                {currentStep === 4 && (
                  <motion.div
                    key="step4"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    {/* Step 4: Final Details */}
                    <div className="mb-8">
                      <div className="flex items-center mb-6">
                        <div className="w-12 h-12 bg-gradient-to-r from-success-500 to-primary-500 rounded-full flex items-center justify-center mr-4">
                          <CheckCircle2 className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Final Details</h2>
                          <p className="text-gray-600 dark:text-gray-400">Almost done! Just a few more details</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        {/* Urgency Level */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Urgency Level *
                          </label>
                          <select
                            value={formData.urgency}
                            onChange={(e) => handleInputChange('urgency', e.target.value)}
                            className={`w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 bg-white/50 dark:bg-gray-700/50 ${
                              errors.urgency
                                ? 'border-error-500 focus:border-error-600'
                                : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                            } focus:ring-2 focus:ring-primary-500/20`}
                          >
                            <option value="">Select urgency</option>
                            <option value="low">Low - No rush</option>
                            <option value="medium">Medium - Standard timeline</option>
                            <option value="high">High - Priority project</option>
                            <option value="critical">Critical - Emergency</option>
                          </select>
                          {errors.urgency && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                            >
                              <AlertCircle className="w-4 h-4 mr-1" />
                              {errors.urgency}
                            </motion.p>
                          )}
                        </div>

                        {/* Preferred Contact Method */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Preferred Contact Method *
                          </label>
                          <select
                            value={formData.preferredContact}
                            onChange={(e) => handleInputChange('preferredContact', e.target.value)}
                            className={`w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 bg-white/50 dark:bg-gray-700/50 ${
                              errors.preferredContact
                                ? 'border-error-500 focus:border-error-600'
                                : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                            } focus:ring-2 focus:ring-primary-500/20`}
                          >
                            <option value="">Select contact method</option>
                            <option value="email">Email</option>
                            <option value="phone">Phone Call</option>
                            <option value="video">Video Call</option>
                            <option value="in-person">In-Person Meeting</option>
                          </select>
                          {errors.preferredContact && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                            >
                              <AlertCircle className="w-4 h-4 mr-1" />
                              {errors.preferredContact}
                            </motion.p>
                          )}
                        </div>
                      </div>

                      {/* Additional Notes */}
                      <div className="mb-6">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Additional Notes (Optional)
                        </label>
                        <textarea
                          value={formData.additionalNotes}
                          onChange={(e) => handleInputChange('additionalNotes', e.target.value)}
                          rows={4}
                          className="w-full px-4 py-3 rounded-xl border-2 border-gray-300 dark:border-gray-600 focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 transition-all duration-200 bg-white/50 dark:bg-gray-700/50 resize-none"
                          placeholder="Any additional information you'd like to share..."
                        />
                      </div>

                      {/* Summary Card */}
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-950/20 dark:to-secondary-950/20 rounded-xl p-6 border border-primary-200 dark:border-primary-800"
                      >
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                          <CheckCircle2 className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2" />
                          Quote Summary
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Service:</span>
                            <span className="ml-2 font-medium text-gray-900 dark:text-white">
                              {serviceOptions.find(s => s.id === formData.service)?.name || 'Not selected'}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Timeline:</span>
                            <span className="ml-2 font-medium text-gray-900 dark:text-white">
                              {formData.timeline.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Not selected'}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Budget:</span>
                            <span className="ml-2 font-medium text-gray-900 dark:text-white">
                              {formData.budget.replace('-', ' - $').replace('k', ',000').replace('under', 'Under $') || 'Not selected'}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Estimated Cost:</span>
                            <span className="ml-2 font-bold text-primary-600 dark:text-primary-400">
                              ${estimatedCost.toLocaleString()}
                            </span>
                          </div>
                        </div>
                        <div className="mt-4 pt-4 border-t border-primary-200 dark:border-primary-800">
                          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <Clock className="w-4 h-4 mr-2" />
                            We'll get back to you within 24 hours with a detailed quote
                          </div>
                        </div>
                      </motion.div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Navigation buttons */}
            <div className="px-8 sm:px-12 pb-8 flex justify-between items-center bg-gray-50/50 dark:bg-gray-900/50">
              <motion.button
                type="button"
                onClick={prevStep}
                disabled={currentStep === 1}
                className={`flex items-center px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
                  currentStep === 1
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
                whileHover={currentStep > 1 ? { x: -4 } : {}}
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Previous
              </motion.button>

              {currentStep < totalSteps ? (
                <motion.button
                  type="button"
                  onClick={nextStep}
                  className="flex items-center px-8 py-3 bg-gradient-to-r from-primary-500 to-secondary-500 text-white font-semibold rounded-xl hover:from-primary-600 hover:to-secondary-600 transition-all duration-300 shadow-lg hover:shadow-xl"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Next Step
                  <ArrowRight className="w-5 h-5 ml-2" />
                </motion.button>
              ) : (
                <motion.button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex items-center px-8 py-3 bg-gradient-to-r from-success-500 to-accent-500 text-white font-semibold rounded-xl hover:from-success-600 hover:to-accent-600 transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                  whileHover={!isSubmitting ? { scale: 1.02 } : {}}
                  whileTap={!isSubmitting ? { scale: 0.98 } : {}}
                >
                  {isSubmitting ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                      Submitting...
                    </>
                  ) : (
                    <>
                      Submit Quote Request
                      <CheckCircle2 className="w-5 h-5 ml-2" />
                    </>
                  )}
                </motion.button>
              )}
            </div>
          </form>
        </motion.div>
      </div>
    </div>
  );
};

export default QuoteRequest;
