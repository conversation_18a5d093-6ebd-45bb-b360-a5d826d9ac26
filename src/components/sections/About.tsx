import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Award, BarChart3, Droplets, Users, Factory, Wrench, HardHat, ClipboardCheck } from 'lucide-react';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { useScroll, useTransform } from 'framer-motion';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

// Updated timelineData based on the new image (3 items)
const timelineData = [
  {
    year: '2012',
    title: 'Foundation',
    description: 'Pazogen was founded with a vision to revolutionize water and waterwaste treatment solutions.',
    side: 'left',
    cardBg: 'bg-blue-500 dark:bg-blue-600',
    cardText: 'text-white',
    yearBg: 'bg-blue-700 dark:bg-blue-800',
    yearText: 'text-white',
    dotColor: 'bg-blue-600 dark:bg-blue-700',
    connectorColor: 'bg-blue-600 dark:bg-blue-700',
  },
  {
    year: '2007',
    title: 'Expansion',
    description: 'Expanded operations across multiple states with innovative treatment technologies.',
    side: 'right',
    cardBg: 'bg-blue-700 dark:bg-blue-800',
    cardText: 'text-white',
    yearBg: 'bg-blue-700 dark:bg-blue-800',
    yearText: 'text-white',
    dotColor: 'bg-blue-600 dark:bg-blue-700',
    connectorColor: 'bg-blue-600 dark:bg-blue-700',
  },
  {
    year: '2012',
    title: 'Innovation',
    description: 'Launched proprietary eco-friendly water filtration system.',
    side: 'left',
    cardBg: 'bg-blue-500 dark:bg-blue-600',
    cardText: 'text-white',
    yearBg: 'bg-blue-700 dark:bg-blue-800',
    yearText: 'text-white',
    dotColor: 'bg-blue-600 dark:bg-blue-700',
    connectorColor: 'bg-blue-600 dark:bg-blue-700',
  },
];

// Define animation variants and transition for background shapes
const bgShapeVariants = {
  hidden: { opacity: 0, scale: 0.8, y: 0 },
  visibleShape1: { opacity: 0.15, scale: 1, y: 20 },
  visibleShape2: { opacity: 0.12, scale: 1, y: -20 },
};

const bgShapeTransition1 = {
  duration: 8,
  repeat: 0,
  ease: 'power2.in',
};

const bgShapeTransition2 = {
  duration: 8,
  repeat: 0,
  ease: 'power2.in',
  delay: 2,
};

const About: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null); // MOVED: Hook declarations to the top of the component
  const {
    scrollYProgress
  } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });
  const y = useTransform(scrollYProgress, [0, 1], ["-20%", "20%"]);

  const [timelineRef, timelineInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const timelineItems = [
    {
      year: '2005',
      title: 'Foundation',
      description: 'Pazogen was founded with a vision to revolutionize water treatment solutions.',
    },
    {
      year: '2010',
      title: 'Expansion',
      description: 'Expanded operations across multiple states with innovative treatment technologies.',
    },
    {
      year: '2015',
      title: 'Innovation',
      description: 'Launched proprietary eco-friendly water filtration system.',
    },
    {
      year: '2020',
      title: 'Global Reach',
      description: 'Began international operations with projects in Europe and Asia.',
    },
    {
      year: '2025',
      title: 'Future Vision',
      description: 'Leading the industry with AI-driven water management solutions.',
    },
  ];

  const stats = [
    { icon: <Award />, value: '2012', label: 'Founded' },
    { icon: <BarChart3 />, value: '100+', label: 'Projects Completed' },
    { icon: <Users />, value: '10+', label: 'Number of Engineers' },
    { icon: <Droplets />, value: 'BBBEE', label: '50% Female Owned' },
  ];

  // Refs for GSAP animations
  const journeyContainerRef = useRef<HTMLDivElement>(null);
  const journeyTrackRef = useRef<HTMLDivElement>(null);
  const journeyLineRef = useRef<HTMLDivElement>(null);
  const journeyItemRefs = useRef<(HTMLDivElement | null)[]>([]);

  useEffect(() => {
    const container = journeyContainerRef.current;
    const track = journeyTrackRef.current; 
    const line = journeyLineRef.current; 
    const items = journeyItemRefs.current.filter(Boolean) as HTMLDivElement[]; 

    if (!container || !track || !line || items.length === 0) return;

    let resizeTimeout: number | null = null;
    const handleResize = () => {
      if (resizeTimeout) clearTimeout(resizeTimeout);
      resizeTimeout = window.setTimeout(() => {
        ScrollTrigger.refresh(true); 
      }, 250);
    };
    window.addEventListener('resize', handleResize);

    // Clear previous ScrollTriggers and animations
    gsap.killTweensOf([line, ...items.flatMap(item => 
      [item.querySelector('.timeline-central-dot'), 
       item.querySelector('.timeline-content-block'), 
       item.querySelector('.connector-to-center-line')
      ]
    )]);
    ScrollTrigger.getAll().forEach(st => st.kill());

    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: container,
        start: "top center-=150", 
        end: () => `+=${Math.max(0, track.scrollHeight - container.offsetHeight + 200)}`, 
        scrub: 1.5,
        // markers: process.env.NODE_ENV === 'development',
      }
    });

    tl.to(line, {
      height: () => Math.max(0, track.scrollHeight - 100), 
      ease: "none",
      duration: timelineData.length * 0.6 
    });

    items.forEach((itemWrapper, idx) => {
      const dot = itemWrapper.querySelector('.timeline-central-dot');
      const contentBlock = itemWrapper.querySelector('.timeline-content-block');
      const card = contentBlock?.querySelector('.actual-event-card'); 
      const yearTag = contentBlock?.querySelector('.year-tag'); 
      const connector = contentBlock?.querySelector('.connector-to-center-line'); 

      const itemData = timelineData[idx]; 

      const itemStartTime = idx * 0.8; 

      if (dot) {
        tl.fromTo(dot,
          { scale: 0.3, autoAlpha: 0 },
          { scale: 1, autoAlpha: 1, ease: "back.out(1.7)", duration: 0.6 },
          itemStartTime
        );
      }

      if (contentBlock && itemData) {
        // Animate the whole content block (card, year tag, connector holder)
        tl.fromTo(contentBlock,
          { 
            xPercent: itemData.side === 'left' ? -40 : 40, 
            autoAlpha: 0 
          },
          { 
            xPercent: 0, 
            autoAlpha: 1, 
            ease: "power3.out", 
            duration: 0.8 
          },
          itemStartTime + 0.15
        );

        // Animate connector line (it's inside contentBlock)
        if (connector) {
          tl.fromTo(connector,
            { scaleX: 0, transformOrigin: itemData.side === 'left' ? 'right center' : 'left center' },
            { scaleX: 1, ease: "power2.out", duration: 0.6 },
            itemStartTime + 0.35 
          );
        }
        
        // Optional: internal animations for card and yearTag if needed, or rely on contentBlock animation
        if (card) {
            gsap.fromTo(card, 
                {y: 20, opacity: 0},
                {y: 0, opacity: 1, duration: 0.5, delay: itemStartTime + 0.45, ease: "power2.out"}
            );
        }
        if (yearTag) {
            gsap.fromTo(yearTag, 
                {y: -10, opacity: 0, scale: 0.8},
                {y: 0, opacity: 1, scale: 1, duration: 0.4, delay: itemStartTime + 0.5, ease: "back.out(1.4)"}
            );
        }

        // Parallax for the content block
        gsap.to(contentBlock, {
            yPercent: -6, 
            ease: "none",
            scrollTrigger: {
                trigger: itemWrapper, 
                containerAnimation: tl.scrollTrigger?.animation, 
                start: "top bottom",
                end: "bottom top",
                scrub: 1.8
            }
        });
      }
    });

    // GSAP Hover animations for cards
    items.forEach((itemWrapper) => {
      const card = itemWrapper.querySelector('.actual-event-card');
      const dot = itemWrapper.querySelector('.timeline-central-dot');
      
      if (card) {
        const hoverTl = gsap.timeline({ paused: true });
        hoverTl.to(card, { 
          y: -8, 
          scale: 1.02,
          boxShadow: "0px 12px 25px rgba(0,0,0,0.15)",
          duration: 0.25, 
          ease: "power1.out" 
        });
        if (dot) {
            const computedDotBg = getComputedStyle(dot).backgroundColor;
            hoverTl.to(dot, { 
                scale: 1.25,
                boxShadow: `0 0 10px 2px ${computedDotBg}`,
                duration: 0.20, 
                ease: "power1.out" 
            }, "<0.03");
        }

        card.addEventListener('mouseenter', () => hoverTl.play());
        card.addEventListener('mouseleave', () => hoverTl.reverse());
      }
    });

    return () => {
      tl.kill();
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
      window.removeEventListener('resize', handleResize);
      if (resizeTimeout) clearTimeout(resizeTimeout);
      items.forEach(itemWrapper => {
        const card = itemWrapper.querySelector('.actual-event-card');
        if (card) {
          // Clean up listeners if necessary (cloneNode is one way, but can be complex)
        }
      });
    };
  }, []);

  // Animation for background elements
  useEffect(() => {
    const bgElements = gsap.utils.toArray<HTMLElement>('[data-animate-bg]');
    bgElements.forEach((el, i) => {
      gsap.to(el, {
        opacity: 0.3,
        duration: 2,
        delay: i * 0.2,
        ease: 'sine.inOut',
        yoyo: false,
        repeat: 0
      });
    });

    return () => {
      gsap.killTweensOf(bgElements);
    };
  }, []);

  return (
    <section ref={sectionRef} id="about" className="py-20 relative overflow-hidden"> 
      {/* Background Image Layer */}
      <motion.div 
        className="absolute inset-0 z-0"
        style={{
          backgroundImage: 'url(/ImageForSite.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          y: y, 
        }}
      />
      {/* Color Overlay Layer for theme tinting */} 
      <div className="absolute inset-0 z-[1] bg-white/[.70] dark:bg-gray-900/[.85]" /> 
      
      {/* Content Wrapper */}
      <div className="relative z-[2]">
        {/* Animated background shapes */}
        <motion.div
          initial="hidden"
          whileInView="visibleShape1"
          variants={bgShapeVariants}
          transition={bgShapeTransition1}
          viewport={{ once: true }}
          className="absolute -top-32 -left-32 w-[400px] h-[400px] rounded-full bg-primary-200 dark:bg-primary-900 blur-3xl z-[2]" 
        />
        <motion.div
          initial="hidden"
          whileInView="visibleShape2"
          variants={bgShapeVariants}
          transition={bgShapeTransition2}
          viewport={{ once: true }}
          className="absolute -bottom-32 -right-32 w-[400px] h-[400px] rounded-full bg-primary-300 dark:bg-primary-800 blur-3xl z-[2]" 
        />
        <div className="container mx-auto px-4 md:px-6 relative z-[10]"> 
          {/* Animated Title */}
          <motion.h2
            initial={{ opacity: 0, y: -40 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
            className="text-3xl md:text-4xl font-display font-extrabold text-center text-primary-700 dark:text-primary-300 mb-6 tracking-tight drop-shadow-lg"
          >
            About Pazogen
          </motion.h2>
          {/* Animated divider */}
          <motion.div
            initial={{ scaleX: 0 }}
            whileInView={{ scaleX: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7, delay: 0.2 }}
            className="mx-auto mb-12 h-1 w-32 bg-gradient-to-r from-primary-400 to-primary-600 rounded-full origin-left"
          />
          {/* Main content */}
          <div className="max-w-4xl mx-auto mb-16">
            {/* Main content */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="space-y-8 text-center"
            >
              <motion.h3
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="text-3xl md:text-4xl font-bold text-primary-800 dark:text-primary-200 mb-4"
              >
                ORIGINAL EQUIPMENT MANUFACTURER
              </motion.h3>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="text-gray-700 dark:text-gray-200 text-lg leading-relaxed"
              >
             
              PAZOGEN is BBBEE and 50% female owned, an original equipment manufacturer of mechanical
              equipment for water and wastewater treatment plants
              </motion.p>
              <motion.div
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={{
                  hidden: {},
                  visible: { transition: { staggerChildren: 0.15 } },
                }}
                className="flex flex-wrap justify-center gap-6 mt-10"
              >
                {stats.map((stat, idx) => (
                  <motion.div
                    key={idx}
                    initial={{ scale: 0.8, opacity: 0 }}
                    whileInView={{ scale: 1, opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.4, delay: 0.1 * idx }}
                    className="flex flex-col items-center bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl px-6 py-5 shadow-lg hover:shadow-xl min-w-[140px] transition-all duration-300 hover:-translate-y-1"
                  >
                    <div className="text-primary-600 dark:text-primary-300 mb-3 text-3xl">{stat.icon}</div>
                    <div className="text-2xl font-bold text-primary-800 dark:text-primary-100 mb-1">
                      {stat.value}
                    </div>
                    <div className="text-sm font-medium text-primary-700/90 dark:text-primary-300/90">{stat.label}</div>
                  </motion.div>
                ))}
              </motion.div>
            </motion.div>
          </div>
          {/* Expertise Section */}
          <div className="w-full py-12 md:py-16 bg-gradient-to-b from-white/50 to-transparent dark:from-gray-900/50 dark:to-transparent">
            <div className="container mx-auto px-4">
              <motion.h3
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="text-3xl md:text-4xl font-display font-bold text-center text-primary-700 dark:text-primary-300 mb-12 tracking-tight"
              >
                Our Expertise
              </motion.h3>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto">
                {[
                  {
                    icon: <Factory className="w-10 h-10 mb-4 text-primary-500" />,
                    title: 'Complete Plant Design',
                    description: 'Comprehensive design solutions for water and wastewater treatment plants from concept to execution.',
                  },
                  {
                    icon: <Wrench className="w-10 h-10 mb-4 text-primary-500" />,
                    title: 'Mechanical Equipment Fabrication & Supply',
                    description: 'Manufacturing and supply of high-quality mechanical equipment tailored for water treatment systems.',
                  },
                  {
                    icon: <HardHat className="w-10 h-10 mb-4 text-primary-500" />,
                    title: 'Installation, Testing & Commissioning',
                    description: 'Expert installation, rigorous testing, and seamless commissioning of all plant components.',
                  },
                  {
                    icon: <ClipboardCheck className="w-10 h-10 mb-4 text-primary-500" />,
                    title: 'Project Management & Construction',
                    description: 'End-to-end project management, material handling, and construction services for timely project delivery.',
                  }
                ].map((item, index) => (
                  <motion.div
                    key={item.title}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.1 * index }}
                    className="expertise-card relative group"
                  >
                    <div className="h-full bg-white/40 dark:bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-white/20 dark:border-gray-700/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-primary-300/30 dark:hover:border-primary-500/30 hover:-translate-y-1">
                      <div className="flex flex-col items-center text-center h-full">
                        <div className="w-16 h-16 flex items-center justify-center rounded-full bg-white/30 dark:bg-gray-700/50 backdrop-blur-sm mb-4 group-hover:bg-primary-100/50 dark:group-hover:bg-primary-900/30 transition-colors duration-300">
                          {item.icon}
                        </div>
                        <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{item.title}</h4>
                        <p className="text-gray-700 dark:text-gray-300 text-sm flex-grow">{item.description}</p>
                        <a 
                          href="#contact" 
                          className="mt-4 inline-block text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 group-hover:underline transition-colors duration-300"
                        >
                          {'Learn more >'}
                        </a>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>

          {/* Values, Vision, Mission Section */}
          <div className="relative z-10 max-w-5xl mx-auto mb-20">
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-8"
            >
              {/* Values */}
              <div className="bg-primary-50 dark:bg-primary-900/40 rounded-2xl shadow-lg p-8 flex flex-col items-center text-center border-t-4 border-primary-400">
                <motion.h4
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  className="text-xl font-bold text-primary-700 dark:text-primary-200 mb-4 tracking-wide"
                >
                  Our Values
                </motion.h4>
                <motion.ul
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  variants={{
                    hidden: {},
                    visible: { transition: { staggerChildren: 0.18 } },
                  }}
                  className="space-y-2 text-primary-800 dark:text-primary-100 text-base font-medium"
                >
                  {['Honesty', 'Integrity', 'Professionalism', 'Transparency', 'Value for money services'].map((value) => (
                    <motion.li
                      key={value}
                      variants={{
                        hidden: { x: -20, opacity: 0 },
                        visible: { x: 0, opacity: 1, transition: { duration: 0.5 } },
                      }}
                    >
                      {value}
                    </motion.li>
                  ))}
                </motion.ul>
              </div>
              {/* Vision */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 flex flex-col items-center text-center border-t-4 border-primary-600">
                <motion.h4
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="text-xl font-bold text-primary-700 dark:text-primary-200 mb-4 tracking-wide"
                >
                  Our Vision
                </motion.h4>
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="text-primary-800 dark:text-primary-100 text-base font-medium"
                >
                  To be the centre of excellence for innovative and technology driven solution for water and wastewater new and existing utilities by harnessing our talents in the application of relevant design and fabrication codes using best engineering practice.
                </motion.p>
              </div>
              {/* Mission */}
              <div className="bg-primary-50 dark:bg-primary-900/40 rounded-2xl shadow-lg p-8 flex flex-col items-center text-center border-t-4 border-primary-400">
                <motion.h4
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="text-xl font-bold text-primary-700 dark:text-primary-200 mb-4 tracking-wide"
                >
                  Our Mission
                </motion.h4>
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="text-primary-800 dark:text-primary-100 text-base font-medium"
                >
                  To provide tailor-made mechanical equipment and serve our clients with best services by being honest and sticking to commitments made to clients.
                </motion.p>
              </div>
            </motion.div>
          </div>

          {/* HORIZONTAL TIMELINE - "Our Journey" - Now a new design */}
          <motion.h3
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-3xl md:text-4xl font-display font-bold text-center text-primary-700 dark:text-primary-300 mb-16 mt-16 tracking-tight drop-shadow-md"
          >
            Our Journey
          </motion.h3>
        
          {/* Container for the NEW VERTICAL timeline */}
          <div ref={journeyContainerRef} className="w-full py-10 md:py-16">
            <div ref={journeyTrackRef} className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
              {/* Central Vertical Line - Ensure z-index allows dots to be on top */}
              <div 
                ref={journeyLineRef} 
                className="absolute left-1/2 top-0 w-1 bg-gray-300 dark:bg-gray-700 transform -translate-x-1/2 rounded-full shadow-md"
                style={{ height: 0, zIndex: 1 }} 
              ></div>

              {timelineData.map((item, idx) => (
                <div 
                  key={item.title + idx} 
                  ref={el => { journeyItemRefs.current[idx] = el; }}
                  className="new-timeline-item-v2 relative py-10 md:py-12" 
                >
                  {/* Central Dot - positioned by its parent's relative, and its own absolute positioning */}
                  <div 
                    className={`timeline-central-dot absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-5 h-5 rounded-full ${item.dotColor} border-2 border-white dark:border-gray-900 shadow-lg z-20 transform-gpu`}
                  ></div>

                  {/* Content Block (Card + Year Tag + Connector placeholder) */}
                  <div 
                    className={`timeline-content-block flex items-center w-full
                              ${item.side === 'left' ? 'justify-start' : 'justify-end'}`}
                  >
                    <div 
                      className={`content-group relative w-11/12 sm:w-6/12 md:w-5/12 lg:w-5/12 
                                ${item.side === 'left' ? 'mr-[calc(50%+1rem)] pr-2 sm:pr-4' : 'ml-[calc(50%+1rem)] pl-2 sm:pl-4'}`}
                    >
                      {/* Actual Card with Glassmorphism */}
                      <div 
                        className={`actual-event-card p-2 sm:p-5 text-center rounded-lg shadow-xl ${item.cardBg} ${item.cardText} bg-opacity-75 dark:bg-opacity-70 backdrop-blur-md backdrop-saturate-125 relative z-10 transform-gpu group`}
                      >
                        {/* Year Tag - positioned relative to the card */}
                        <div 
                          className={`year-tag absolute -top-3.5 ${item.side === 'left' ? 'left-4' : 'right-4'} ${item.yearBg} ${item.yearText} py-1 px-3 rounded text-xs sm:text-sm font-medium shadow-md z-20`}
                        >
                          {item.year}
                        </div>
                        <h5 className="font-semibold text-md sm:text-lg mb-1.5 mt-1">{item.title}</h5>
                        <p className="text-xs sm:text-sm leading-normal opacity-90">{item.description}</p>
                      </div>
                      
                      {/* Connector Line from card towards center */}
                      <div 
                        className={`connector-to-center-line absolute top-1/2 -translate-y-1/2 h-[3px] ${item.connectorColor} rounded-full z-0
                                      ${item.side === 'left' ? 'right-0 w-3 sm:w-4' : 'left-0 w-3 sm:w-4'}`}
                                      // Width defined here for the connector line itself
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

        </div>
      </div> {/* End Content Wrapper */}
      {/* End Leadership Structure Section */}
      
    </section>
  );
};

export default About;