import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { ChevronDown, Droplet, Phone, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import gsap from 'gsap';

// Glass card component
const GlassCard: React.FC<{ className?: string; children: React.ReactNode }> = ({ children, className = '' }) => {
  const gradientRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (gradientRef.current) {
      // GSAP animation for a subtle, shifting gradient
      gsap.to(gradientRef.current, {
        '--gradient-color-1': 'rgba(59, 130, 246, 0.2)', // blue-500 with opacity
        '--gradient-color-2': 'rgba(96, 165, 250, 0.3)', // blue-400 with opacity
        '--gradient-color-3': 'rgba(34, 211, 238, 0.2)', // cyan-400 with opacity
        duration: 7,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
      });
      gsap.to(gradientRef.current, {
        backgroundPosition: '200% center',
        duration: 15,
        repeat: -1,
        ease: 'linear',
      })
    }
  }, []);

  return (
    <div
      className={`relative backdrop-blur-xl bg-white/5 dark:bg-gray-900/5 border border-white/10 dark:border-gray-700/30 rounded-2xl shadow-2xl overflow-hidden transition-all duration-300 hover:shadow-3xl ${className}`}
      id="home"
    >
      <div
        ref={gradientRef}
        className="absolute inset-0 -z-10 opacity-70"
        style={{
          backgroundImage: 'linear-gradient(60deg, var(--gradient-color-1, rgba(59, 130, 246, 0.1)), var(--gradient-color-2, rgba(96, 165, 250, 0.15)) 50%, var(--gradient-color-3, rgba(34, 211, 238, 0.1)) 100%)',
          backgroundSize: '300% 300%', // Made larger for smoother animation
        }}
      />
      {children}
    </div>
  );
};

const Hero: React.FC = () => {
  const heroRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Floating animation for elements (kept from original)
    const floatingElements = document.querySelectorAll('.floating-element');
    floatingElements.forEach((el) => {
      gsap.to(el, {
        y: 8, // Slightly reduced y for subtlety
        duration: 3.5,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
      });
    });
  }, []);
  
  // Updated to use landingpage.jpg from the public folder
  const heroBackgroundImage = 'url("/landingpage.jpg")'; 

  return (
    <section 
      ref={heroRef} 
      className="min-h-screen relative overflow-hidden flex items-center justify-center md:justify-start bg-cover bg-center p-4 md:p-8 w-full" 
      style={{
        backgroundImage: heroBackgroundImage,
        backgroundAttachment: 'fixed', // Added for parallax effect
      }}
    >
      {/* Overlay: Neutral semi-transparent black for a glass effect */}
      <div className="absolute inset-0 bg-black/40 dark:bg-black/60 -z-0"></div>
      
      {/* Content container: adjusted for left alignment on medium screens and up */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 w-full md:w-auto md:ml-[5%] lg:ml-[10%]">
        <GlassCard className="p-8 md:p-10 lg:p-12 w-full md:max-w-xl lg:max-w-2xl"> 
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="text-left" 
          >
           
            
            <h1 className="text-3xl sm:text-4xl md:text-[2.6rem] lg:text-5xl font-extrabold text-white dark:text-gray-50 mb-5 leading-tight tracking-tight antialiased">
              An Original Equipment Manufacturer (OEM) for Water and Wastewater Engineering
            </h1>
            
            <p className="text-lg md:text-xl text-gray-200 dark:text-gray-300 mb-10 max-w-3xl leading-relaxed antialiased">
              PAZOGEN is your trusted partner in water and wastewater solutions.
            </p>
            
            {/* Enhanced CTA Button */}
            <motion.div 
              className="flex flex-col sm:flex-row gap-4 justify-start"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
            >
              <Link 
                to="/get-a-quote"
                className="group px-6 py-3.5 bg-gradient-to-r from-blue-500 to-cyan-400 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-cyan-500 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 text-center inline-flex items-center justify-center"
              >
                GET A FREE QUOTE
                <ArrowRight className="w-5 h-5 ml-2 transition-transform duration-300 ease-in-out group-hover:translate-x-1" />
              </Link>
            </motion.div>
          </motion.div>
        </GlassCard>
      </div>
      
      {/* WhatsApp Floating Action Button */}
      <div className="fixed bottom-6 right-6 md:bottom-8 md:right-8 z-50 group">
        <a 
          href="https://wa.me/+27659643597"
          target="_blank" 
          rel="noopener noreferrer"
          className="relative overflow-hidden backdrop-blur-xl bg-white/10 dark:bg-gray-900/10 border border-white/10 dark:border-gray-700/30 text-white p-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 ease-in-out flex items-center justify-center group-hover:scale-110"
          aria-label="Chat on WhatsApp"
        >
          <div className="absolute inset-0 -z-10 opacity-70 animate-gradient" 
               style={{
                 background: 'linear-gradient(60deg, rgba(59, 130, 246, 0.2), rgba(96, 165, 250, 0.3) 50%, rgba(34, 211, 238, 0.2) 100%)',
                 backgroundSize: '300% 300%',
                 animation: 'gradient 7s ease infinite',
               }}
          />
          <Phone size={28} className="relative z-10" />
        </a>
      </div>
      
      <style jsx global>{`
        @keyframes gradient {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
      `}</style>

      {/* Scroll indicator (kept) */}
      <motion.div 
        className="absolute bottom-28 md:bottom-12 left-0 right-0 mx-auto w-fit flex flex-col items-center z-10" 
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1, duration: 0.8, ease: 'easeOut' }}
      >
        <span className="text-sm text-gray-200 dark:text-gray-300 mb-2 text-center">Scroll to explore</span>
        <ChevronDown className="w-6 h-6 text-gray-200 dark:text-gray-300 animate-bounce" />
      </motion.div>
    </section>
  );
};

export default Hero;