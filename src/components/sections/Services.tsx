import React, { useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { 
  Factory, 
  Lightbulb, 
  ClipboardList, 
  Filter, 
  Recycle, 
  Wrench,
  ArrowRight
} from 'lucide-react';

interface ServiceCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  index: number;
  image?: string;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ icon, title, description, index, image }) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 30 }}
      animate={inView ? { opacity: 1, y: 0 } : {}}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ y: -8 }}
      className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg relative overflow-hidden group"
    >
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-400 to-secondary-400 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
      {image && (
        <div className="mb-4 w-full aspect-video rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-900 flex items-center justify-center">
          <img src={image} alt={title} className="object-cover w-full h-full" loading="lazy" />
        </div>
      )}
      <div className="mb-4 text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/30 p-3 rounded-lg inline-block">
        {icon}
      </div>
      <h3 className="text-xl font-display font-semibold text-gray-900 dark:text-white mb-3">
        {title}
      </h3>
      <p className="text-gray-600 dark:text-gray-300 mb-4">
        {description}
      </p>
      <a 
        href="#contact" 
        className="inline-flex items-center text-primary-600 dark:text-primary-400 font-medium group-hover:text-primary-700 dark:group-hover:text-primary-300 transition-colors"
      >
        Learn more 
        <motion.span 
          initial={{ x: 0 }} 
          whileHover={{ x: 4 }}
          className="ml-1"
        >
          <ArrowRight size={16} />
        </motion.span>
      </a>
    </motion.div>
  );
};

const Services: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const bgRef = useRef<HTMLDivElement>(null);
  
  // Enhanced scroll-based parallax effect
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });
  
  // Vertical parallax movement
  const y = useTransform(scrollYProgress, [0, 1], ["-30%", "30%"]);
  const services = [
    {
      title: 'Screen Equipment',
      image: '/ScreenEquipment.jpeg',
      description: [
        '• Mechanical Front Rake screens',
        '• Fine and ultra-fine screens',
        '• Manual hand rake screens',
        '• Perforated screens',
      ].join('\n'),
    },
    {
      title: 'Flow Control',
      image: '/FlowControl.jpeg',
      description: [
        '• Sluice gate and Penstocks',
        '• Hand stops',
        '• Tilting Weirs',
        '• Stop Logs',
        '• Downwards opening gates',
      ].join('\n'),
    },
    {
      title: 'Clarifiers',
      image: '/Clarifiers.jpeg',
      description: [
        '• Peripheral drive settling tanks',
        '• Peripheral drive suction lift',
      ].join('\n'),
    }
  ];

  return (
    <section 
      id="services" 
      ref={sectionRef}
      className="py-20 bg-gray-50 dark:bg-gray-900/95 relative overflow-hidden"
    >
      {/* Background Image with Parallax Effect */}
      <motion.div 
        ref={bgRef}
        className="absolute inset-0 w-full h-full bg-cover bg-center opacity-95"
        style={{
          backgroundImage: "url('/backgroundServices.png')",
          backgroundAttachment: 'fixed',
          y: y,
          willChange: 'transform',
          transition: 'transform 0.1s ease-out'
        }}
      />
      
      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-white/80 via-transparent to-white/80 dark:from-gray-900/90 dark:via-gray-900/95 dark:to-gray-900/90" />
      
      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-3xl md:text-4xl font-display font-bold text-gray-900 dark:text-white mb-4"
          >
            Our <span className="text-primary-600 dark:text-primary-400">Services</span>
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="text-gray-600 dark:text-gray-300 text-lg"
          >
            We provide comprehensive water treatment solutions tailored to meet the unique 
            needs of municipalities, industries, and commercial developments.
          </motion.p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <ServiceCard
              key={index}
              icon={null}
              title={service.title}
              description={service.description}
              image={service.image}
              index={index}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Services;